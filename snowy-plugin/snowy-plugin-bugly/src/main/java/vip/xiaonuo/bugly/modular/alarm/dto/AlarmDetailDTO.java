package vip.xiaonuo.bugly.modular.alarm.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import vip.xiaonuo.bugly.core.config.BuglyAppConfig;
import vip.xiaonuo.bugly.modular.syncInfo.entity.AppAuth;
import vip.xiaonuo.bugly.modular.syncInfo.entity.AppInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 告警信息详情传输对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/1/20 15:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "告警信息详情传输对象")
public class AlarmDetailDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 告警基本信息ID
     */
    @ApiModelProperty(value = "告警基本信息ID", position = 1)
    private String id;

    /**
     * 告警名称
     */
    @ApiModelProperty(value = "告警名称", position = 2)
    private String alarmName;
    /**
     * 统计周期
     */
    @ApiModelProperty(value = "统计周期", position = 3)
    private String alarmType;

    /**
     * 接收方式,JSONString
     */
    @ApiModelProperty(value = "接收方式,JSONString", name = "notifyType", required = true)
    private String notifyType;

    /**
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID", position = 5)
    private String appId;

    /**
     * APP版本号(该字段非空则告警作用于特定版本)
     */
    @ApiModelProperty(value = "APP版本号(该字段非空则告警作用于特定版本)", position = 6)
    private String appVersion;

    /**
     * APP渠道号(该字段非空则告警作用于特定渠道)
     */
    @ApiModelProperty(value = "APP渠道号(该字段非空则告警作用于特定渠道)", position = 7)
    private String appChannel;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", name = "description")
    private String description;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createUser")
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createTime", required = true)
    private Date createTime;
    /**
     * 最后更新人
     */
    @ApiModelProperty(value = "最后更新人", name = "updateUser")
    private String updateUser;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间", name = "updateTime", required = true)
    private Date updateTime;
    /**
     * 状态，-1删除，0无效，1有效
     */
    @ApiModelProperty(value = "状态，-1删除，0无效，1有效", name = "status", required = true)
    private String status;

    /**
     * 关联的应用信息详情
     */
    @ApiModelProperty(value = "关联的应用信息详情", name = "appInfo")
    private AppInfo appInfo;

    /**
     * 应用信息关联的权限详情
     */
    @ApiModelProperty(value = "应用信息关联的权限详情", name = "appAuthList")
    private List<AppAuth> appAuthList;

    /**
     * 关联的告警条件详情
     */
    @ApiModelProperty(value = "关联的告警条件详情", name = "conditionDetailDTOList")
    private List<ConditionDetailDTO> conditionDetailDTOList;

    /**
     * 接收方式,List
     */
    @ApiModelProperty(value = "接收方式,List", name = "notifyTypes", required = true)
    private List<String> notifyTypes;
    /**
     * 告警接收人,List
     */
    @ApiModelProperty(value = "告警接收人,List", name = "receiveUsersWarn", required = true)
    private List<Integer> receiveUsersWarn;
    /**
     * 告警抄送人,List
     */
    @ApiModelProperty(value = "告警抄送人,List", name = "ccUsersWarn")
    private List<Integer> ccUsersWarn;
    /**
     * 报告接收人,List
     */
    @ApiModelProperty(value = "报告接收人,List", name = "receiveUsersReport", required = true)
    private List<Integer> receiveUsersReport;
    /**
     * 报告抄送人,List
     */
    @ApiModelProperty(value = "报告抄送人,List", name = "ccUsersReport")
    private List<Integer> ccUsersReport;

    public List<String> getNotifyTypes() {
        if (StringUtils.isNotBlank(notifyType)) {
            notifyTypes = JSONObject.parseArray(notifyType, String.class);
        }
        return notifyTypes;
    }

    public List<Integer> getReceiveUsersWarn() {
        receiveUsersWarn = new ArrayList<>();
        if (StringUtils.containsIgnoreCase(this.getAlarmName(), "测试")) {
            receiveUsersWarn.add(8257);
        } else {
            if (CollectionUtils.isNotEmpty(appAuthList)) {
                for (AppAuth appAuth : appAuthList) {
                    if (!Integer.valueOf(0).equals(appAuth.getReceiveWarn()) && !receiveUsersWarn.contains(appAuth.getUserId())) {
                        CollectionUtils.addIgnoreNull(receiveUsersWarn, appAuth.getUserId());
                    }
                }
            }
        }
        return receiveUsersWarn;
    }

    public List<Integer> getCcUsersWarn() {
//        if (CollectionUtils.isNotEmpty(appAuthList)) {
//            ccUsersWarn = new ArrayList<>();
//            for (AppAuth appAuth : appAuthList) {
//                if (Integer.valueOf(2).equals(appAuth.getReceiveWarn()) && !ccUsersWarn.contains(appAuth.getUserId())) {
//                    CollectionUtils.addIgnoreNull(ccUsersWarn, appAuth.getUserId());
//                }
//            }
//        }
        ccUsersWarn = new ArrayList<>();
        if (StringUtils.containsIgnoreCase(this.getAlarmName(), "测试")) {
            ccUsersWarn.add(5323);
            ccUsersWarn.add(8193);
        } else {
            // 添加bugly管理员作为抄送人
            if (CollectionUtils.isNotEmpty(BuglyAppConfig.BUGLY_ADMINISTRATOR_USERIDS)) {
                for (String userId : BuglyAppConfig.BUGLY_ADMINISTRATOR_USERIDS) {
                    CollectionUtils.addIgnoreNull(ccUsersWarn, Integer.valueOf(userId));
                }
            }
            // 添加额外配置的抄送人
            if (CollectionUtils.isNotEmpty(BuglyAppConfig.BUGLY_CC_USERIDS)) {
                for (String userId : BuglyAppConfig.BUGLY_CC_USERIDS) {
                    Integer ccUserId = Integer.valueOf(userId);
                    if (!ccUsersWarn.contains(ccUserId)) {
                        CollectionUtils.addIgnoreNull(ccUsersWarn, ccUserId);
                    }
                }
            }
        }
        return ccUsersWarn;
    }

    public List<Integer> getReceiveUsersReport() {
        receiveUsersReport = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(appAuthList)) {
            for (AppAuth appAuth : appAuthList) {
                if (!Integer.valueOf(0).equals(appAuth.getReceiveReport()) && !receiveUsersReport.contains(appAuth.getUserId())) {
                    CollectionUtils.addIgnoreNull(receiveUsersReport, appAuth.getUserId());
                }
            }
        }
        return receiveUsersReport;
    }

    public List<Integer> getCcUsersReport() {
//        if (CollectionUtils.isNotEmpty(appAuthList)) {
//            for (AppAuth appAuth : appAuthList) {
//                if (Integer.valueOf(2).equals(appAuth.getReceiveReport()) && !ccUsersReport.contains(appAuth.getUserId())) {
//                    CollectionUtils.addIgnoreNull(ccUsersReport, appAuth.getUserId());
//                }
//            }
//        }
        ccUsersReport = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(BuglyAppConfig.BUGLY_ADMINISTRATOR_USERIDS)) {
            for (String userId : BuglyAppConfig.BUGLY_ADMINISTRATOR_USERIDS) {
                CollectionUtils.addIgnoreNull(ccUsersReport, Integer.valueOf(userId));
            }
        }
        return ccUsersReport;
    }
}
