#########################################
# server configuration
#########################################
server.port=8082

#########################################
# multipart configuration
#########################################
spring.servlet.multipart.max-request-size=100MB
spring.servlet.multipart.max-file-size=100MB

#########################################
# datasource configuration
#########################################

# mysql
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.master.url=**************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=root
spring.datasource.dynamic.datasource.master.password=root@0808
spring.datasource.dynamic.strict=true

# biz mysql
#spring.datasource.dynamic.datasource.biz.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.biz.url=********************************************************************************************************
spring.datasource.dynamic.datasource.biz.username=root
spring.datasource.dynamic.datasource.biz.password=root@0808
spring.datasource.dynamic.datasource.biz.lazy=true

# monitor mysql
#spring.datasource.dynamic.datasource.biz.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.monitor.url=**********************************************************************************************************
spring.datasource.dynamic.datasource.monitor.username=root
spring.datasource.dynamic.datasource.monitor.password=root@0808
spring.datasource.dynamic.datasource.monitor.lazy=true

# bugly_monitor mysql
#spring.datasource.dynamic.datasource.buglymonitor.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.buglymonitor.url=****************************************************************************************************************************************
spring.datasource.dynamic.datasource.buglymonitor.username=srv_jjbugly_monitor_ro
spring.datasource.dynamic.datasource.buglymonitor.password=MnceILTR8Zz7d4p
spring.datasource.dynamic.datasource.buglymonitor.lazy=true

# release_monitor mysql
#spring.datasource.dynamic.datasource.releasemonitor.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.releasemonitor.url=**************************************************************************************************************
spring.datasource.dynamic.datasource.releasemonitor.username=root
spring.datasource.dynamic.datasource.releasemonitor.password=root@0808
spring.datasource.dynamic.datasource.releasemonitor.lazy=true

# enemy_message mysql
#spring.datasource.dynamic.datasource.enemymessage.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.enemymessage.url=************************************************************************************************************
spring.datasource.dynamic.datasource.enemymessage.username=root
spring.datasource.dynamic.datasource.enemymessage.password=root@0808
spring.datasource.dynamic.datasource.enemymessage.lazy=true

# cloud mysql
#spring.datasource.dynamic.datasource.biz.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.cloud.url=***************************************************************************************************************
spring.datasource.dynamic.datasource.cloud.username=root
spring.datasource.dynamic.datasource.cloud.password=root@0808
spring.datasource.dynamic.datasource.cloud.lazy=true

# sample_collection mysql
#spring.datasource.dynamic.datasource.biz.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.collection.url=****************************************************************************************************************
spring.datasource.dynamic.datasource.collection.username=root
spring.datasource.dynamic.datasource.collection.password=root@0808
spring.datasource.dynamic.datasource.collection.lazy=true

# jjneural mysql
#spring.datasource.dynamic.datasource.biz.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.jjneural.url=**********************************************************************************************************************************
spring.datasource.dynamic.datasource.jjneural.username=srv_monitor_rwl
spring.datasource.dynamic.datasource.jjneural.password=HEmQITRGPNe5aM3
spring.datasource.dynamic.datasource.jjneural.lazy=true

# clickhouse
spring.datasource.dynamic.datasource.clickhouse.driver-class-name=ru.yandex.clickhouse.ClickHouseDriver
spring.datasource.dynamic.datasource.clickhouse.url=*************************************************************
spring.datasource.dynamic.datasource.clickhouse.username=chenjian01
spring.datasource.dynamic.datasource.clickhouse.password=chenjian01
spring.datasource.dynamic.datasource.clickhouse.lazy=true

# oracle
#spring.datasource.dynamic.datasource.master.driver-class-name=oracle.jdbc.driver.OracleDriver
#spring.datasource.dynamic.datasource.master.url=***********************************************************
#spring.datasource.dynamic.datasource.master.username=SNOWY
#spring.datasource.dynamic.datasource.master.password=12345678
#spring.datasource.dynamic.strict=true

# mssql
#spring.datasource.dynamic.datasource.master.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
#spring.datasource.dynamic.datasource.master.url=**************************************************
#spring.datasource.dynamic.datasource.master.username=sa
#spring.datasource.dynamic.datasource.master.password=123456
#spring.datasource.dynamic.strict=true

# dm database
#spring.datasource.dynamic.datasource.master.driver-class-name=dm.jdbc.driver.DmDriver
#spring.datasource.dynamic.datasource.master.url=jdbc:dm://localhost:5236/SYSDBA
#spring.datasource.dynamic.datasource.master.username=SYSDBA
#spring.datasource.dynamic.datasource.master.password=SYSDBA
#spring.datasource.dynamic.strict=true

# druid monitor configuration
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.login-username=admin
spring.datasource.druid.stat-view-servlet.login-password=123456

# druid global configuration
spring.datasource.dynamic.public-key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMWiTVtdXFVrgFHDDKELZM0SywkWY3KjugN90eY5Sogon1j8Y0ClPF7nx3FuE7pAeBKiv7ChIS0vvx/59WUpKmUCAwEAAQ==
spring.datasource.dynamic.druid.initial-size=5
spring.datasource.dynamic.druid.max-active=20
spring.datasource.dynamic.druid.min-idle=5
spring.datasource.dynamic.druid.max-wait=60000
spring.datasource.dynamic.druid.pool-prepared-statements=true
spring.datasource.dynamic.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.dynamic.druid.validation-query-timeout=2000
spring.datasource.dynamic.druid.test-on-borrow=false
spring.datasource.dynamic.druid.test-on-return=false
spring.datasource.dynamic.druid.test-while-idle=true
spring.datasource.dynamic.druid.time-between-eviction-runs-millis=60000
spring.datasource.dynamic.druid.min-evictable-idle-time-millis=300000
spring.datasource.dynamic.druid.filters=stat
spring.datasource.dynamic.druid.break-after-acquire-failure=false

#########################################
# jackson configuration
#########################################
spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.locale=zh_CN

#########################################
# redis configuration
#########################################
spring.redis.database=1
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
spring.redis.timeout=10s

spring.redis.lettuce.pool.max-active=200
spring.redis.lettuce.pool.max-wait=-1ms
spring.redis.lettuce.pool.max-idle=10
spring.redis.lettuce.pool.min-idle=0

#########################################
# mybatis-plus configuration
#########################################
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.configuration.jdbc-type-for-null=null
mybatis-plus.global-config.banner=false
mybatis-plus.global-config.db-config.id-type=ASSIGN_ID
mybatis-plus.global-config.db-config.logic-delete-field=DELETE_FLAG
mybatis-plus.global-config.db-config.logic-delete-value=DELETED
mybatis-plus.global-config.db-config.logic-not-delete-value=NOT_DELETE
mybatis-plus.mapper-locations=classpath*:vip/xiaonuo/**/mapping/*.xml
mybatis-plus.type-handlers-package=vip.xiaonuo.common.handler

#########################################
# easy-trans configuration
#########################################
easy-trans.is-enable-redis=true
easy-trans.is-enable-global=true
easy-trans.is-enable-tile=true
easy-trans.is-enable-cloud=false

#########################################
# sa-token configuration
#########################################
sa-token.token-name=token
sa-token.timeout=2592000
sa-token.activity-timeout=-1
sa-token.is-concurrent=true
sa-token.is-share=false
sa-token.max-login-count=-1
sa-token.token-style=random-32
sa-token.is-log=false
sa-token.is-print=false
sa-token.basic=sa:yhScWcDx60jk

# sa-token alone-redis configuration
sa-token.alone-redis.database=2
sa-token.alone-redis.host=${spring.redis.host}
sa-token.alone-redis.port=${spring.redis.port}
sa-token.alone-redis.password=${spring.redis.password}
sa-token.alone-redis.timeout=${spring.redis.timeout}
sa-token.alone-redis.lettuce.pool.max-active=${spring.redis.lettuce.pool.max-active}
sa-token.alone-redis.lettuce.pool.max-wait=${spring.redis.lettuce.pool.max-wait}
sa-token.alone-redis.lettuce.pool.max-idle=${spring.redis.lettuce.pool.max-idle}
sa-token.alone-redis.lettuce.pool.min-idle=${spring.redis.lettuce.pool.min-idle}

#########################################
# knife4j configuration
#########################################
knife4j.enable=true
knife4j.production=false
knife4j.basic.enable=true
knife4j.basic.username=admin
knife4j.basic.password=123456
knife4j.setting.enableOpenApi=false
knife4j.setting.enableSwaggerModels=false
knife4j.setting.enableFooter=false
knife4j.setting.enableFooterCustom=true
knife4j.setting.footerCustomContent=Apache License 2.0 | Copyright 2022-[SNOWY](https://www.xiaonuo.vip)

# freemarker是否开启缓存
spring.freemarker.cache=false
spring.freemarker.charset=UTF-8
# freemarker模板文件目录
# 模板文件在classpath路径目录下
#spring.freemarker.template-loader-path=classpath:/templates/
# 模板文件在jar包相对路径目录下
spring.freemarker.template-loader-path=file:./templates/
# freemarker模板文件后缀
spring.freemarker.suffix=.ftl

# Kafka 相关配置
# Kafka 服务器地址
spring.kafka.bootstrap-servers=HB1197-broker01.bd.jjworld.tech:9092,HB1197-broker02.bd.jjworld.tech:9092,HB1197-broker03.bd.jjworld.tech:9092,HB1197-broker04.bd.jjworld.tech:9092,HB1197-broker05.bd.jjworld.tech:9092
spring.kafka.properties.sasl.mechanism: PLAIN
spring.kafka.properties.security.protocol: SASL_PLAINTEXT
spring.kafka.properties.max.poll.interval.ms: 300000
spring.kafka.properties.sasl.jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username="jj_neural_enemy" password="cJeTRYf91sXp";
# 消费者配置
spring.kafka.consumer.group-id=jj_neural_enemy_2000016_cg
# earliest 或 latest
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.auto-commit-interval=1000
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

#########################################
# snowy configuration
#########################################

# common configuration
snowy.config.common.front-url=http://localhost:81
snowy.config.common.backend-url=/api/snowy

# riskTag configuration
snowy.config.tags.url=http://************:8080
snowy.config.tags.username=risk_control
snowy.config.tags.password=rs957C7fXMCw

# data configuration
snowy.config.data.url=http://************:8085/data

# userInfo configuration
snowy.config.userInfo.url=https://mis.jjmatch.cn

# 自定义配置
# 系统管理员用户ID(用于接收系统消息等)
app-settings.system.administrator.userids=8257
# MPS管理员用户ID(用于接收MPS数据异常等消息)
app-settings.mps.administrator.userids=9428
# DPS管理员用户ID(用于接收DPS数据异常等消息)
app-settings.dps.administrator.userids=8257
# tag管理员用户ID(用于接收风控标签数据异常等消息)
app-settings.tag.administrator.userids=9465
# bugly管理员用户ID(用于bugly数据异常等消息)
app-settings.bugly.administrator.userids=8193,8257,5323
# bugly告警邮件额外抄送人用户ID(用于告警邮件抄送)
app-settings.bugly.cc.userids=98,5213

# ESB相关配置
# ESB服务器地址
app-config.esb.url=https://esb.mis.jjmatch.cn
# ESB账号
app-config.esb.account=e519f2
# ESB密码
app-config.esb.password=6be84ee28206
# 开启信鸽通知(默认true)
app-settings.esb.xinge.enabled=true
# 开启Email通知(默认true)
app-settings.esb.email.enabled=true
# ECharts Export Server相关配置
# ECharts Export Server服务器地址(如果为空则关闭图片导出功能)
app-config.echarts-export-server.url=http://localhost:3000/
# 接口开放平台(OpenApi) 相关配置
# OpenApi服务器地址
app-config.open-api.url=http://open.jjweb.cn
app-config.open-api.account=12-VsGhJi3wLSdY
app-config.open-api.password=CacvQCvRndWLimDC
# AI接口 相关配置
# AI服务器地址
app-config.ai.url=http://************:5678/webhook

# MIS查询数据API接口 相关配置
# Bugly2.0数据查询服务器地址
# (国内)
app-config.mis-bugly-api.url=http://**************:8080/
# (海外版)
app-config.mis-bugly-overseas-api.url=https://mis.jjmatch.cn/cmec/usbugly/view/api/v2/

# 网络终端管理接口 相关配置
# 网络终端管理服务器地址
app-config.network-manage.url=https://mis.jjmatch.cn/gw/sec-itassets

# MPS和DPS云控规则管理 相关配置
# 云控规则管理服务器地址
app-config.release.rule-manage.url=http://*************:8080

# ES代理服务器地址
app-config.es.url=https://mis.jjmatch.cn

# 群机器人消息token配置
# 游戏ID对应token
# 默认token,默认为测试群 https://oapi.dingtalk.com/robot/send?access_token=499bbcb43bb9704c3e6a8f46ecab9de92133a5d50a938e798455439df94366e5
app-config.robot.message.token-of-default=499bbcb43bb9704c3e6a8f46ecab9de92133a5d50a938e798455439df94366e5
# 平台棋牌游戏风控对接群 https://oapi.dingtalk.com/robot/send?access_token=ffd13d88ef5930d1b64135e5b45279bb4f89859b165e92156d6ec92dab9b22d8
# 地方麻将风控对接群 https://oapi.dingtalk.com/robot/send?access_token=7c9c997cc3e51d3413a761b82e84cca690e4404266c53ddf3b666bd03dda60e6
app-config.robot.message.token-of-game-id=1017,1056,1076,1031,1084,1081,1035,1007,1125:ffd13d88ef5930d1b64135e5b45279bb4f89859b165e92156d6ec92dab9b22d8;1022,1134,1074,1107,1106,1105,1104,1102,1101,1100,1109,1115,1113,1099,1098,1051,1094,1126:7c9c997cc3e51d3413a761b82e84cca690e4404266c53ddf3b666bd03dda60e6
# 标签英文名对应token
app-config.robot.message.token-of-tag=is_login_mps_apk_certificate_sha1_ab_15d:499bbcb43bb9704c3e6a8f46ecab9de92133a5d50a938e798455439df94366e5

# 敌情线索告警群机器人token(所有)
# 威胁情报告警：0b6774540c163c03dc0248607df11f12a4fe7b58603269c2c807312f3d0e47ff
app-config.robot.enemy.msg.token=0b6774540c163c03dc0248607df11f12a4fe7b58603269c2c807312f3d0e47ff
# 敌情线索告警群机器人token(曙光英雄)
# 威胁情报告警-曙光英雄：648bbc16d32cd3ca0a007aa0cf1e75b09c4fc2ddde080c8af4834181b54bd940
app-config.robot.enemy.msg.tokenOfShuguang=648bbc16d32cd3ca0a007aa0cf1e75b09c4fc2ddde080c8af4834181b54bd940
# 敌情线索AI检测结果邮件收件人，多个用逗号分隔
app-config.enemy.msg.email.to=9137,7572,4369,5006,10300
# 敌情线索AI检测结果邮件抄送人，多个用逗号分隔
app-config.enemy.msg.email.cc=98,5213,10675,5323,8257

# 策略管理相关配置
# python命令
app-config.policy.template.python.cmd=python3
app-config.policy.template.file.path.py=/home/<USER>/b31143/src/B31143_pro.py
app-config.policy.template.file.path.out=/home/<USER>/b31143/out
app-config.policy.template.manage.token=03a5f821-2778-591c-1ae4-ac7bf927ac20
# 文件下载 baseUrl
app-config.policy.download.oss.base-url=http://jjdpsbucket.oss.jjweb.cn/pcfile/server/

# mdps 策略下发服务
app-config.cloud.mdps-policy-server.url=http://192.168.155.146:8080/api/v1/mdps/policy/

# MQTT相关配置
# MQTT服务器地址
app-config.mqtt.url=tcp://192.168.130.47:1883
# MQTT用户名
app-config.mqtt.username=
# MQTT密码
app-config.mqtt.password=
# MQTT图片识别配置
# 图片识别客户端ID
app-config.mqtt.image.client-id=snowy-ai-image-client
# 图片识别请求Topic
app-config.mqtt.image.request-topic=ai/oid/detect/request
# 图片识别响应Topic
app-config.mqtt.image.response-topic=ai/oid/detect/response
# 是否启用MQTT图片识别
app-config.mqtt.image.enabled=true

# MQTT文本识别配置
# 文本识别客户端ID
app-config.mqtt.text.client-id=snowy-ai-text-client
# 文本识别请求Topic
app-config.mqtt.text.request-topic=ai/text/detect/request
# 文本识别响应Topic
app-config.mqtt.text.response-topic=ai/text/detect/response
# 是否启用MQTT文本识别
app-config.mqtt.text.enabled=true
